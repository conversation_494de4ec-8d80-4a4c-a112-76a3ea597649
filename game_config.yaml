players:
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON><PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "<PERSON>"
  - "Greer"

models:
  # Locally available Ollama models (verified working)
  - "hf.co/unsloth/gemma-3-1b-it-GGUF:Q4_K_M"
  - "hf.co/lmstudio-community/Nemotron-Research-Reasoning-Qwen-1.5B-GGUF:Q4_K_M"
  - "hf.co/lmstudio-community/ZR1-1.5B-GGUF:Q4_K_M"
  - "hf.co/lmstudio-community/internlm2_5-1_8b-chat-GGUF:Q4_K_M"
  - "hf.co/mradermacher/MiniCPM4-0.5B-GGUF:Q4_K_M"
  - "hf.co/lmstudio-community/granite-3.1-1b-a400m-instruct-GGUF:Q4_K_M"
  - "hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M"
  - "hf.co/unsloth/Llama-3.2-1B-Instruct-GGUF:Q4_K_M"

  # Previous cloud-based models (commented out for reference)
  #- "openai/gpt-4.1"
  #- "openai/o1"
  #- "google/gemini-2.5-pro-preview"
  #- "google/gemini-2.5-flash-preview-05-20:thinking"
  #- "qwen/qwen3-32b"
  #- "qwen/qwen3-235b-a22b"
  #- "qwen/qwq-32b"
  #- "anthropic/claude-3.7-sonnet"
  #- "anthropic/claude-sonnet-4"
  #- "anthropic/claude-opus-4"
  #- "x-ai/grok-3-beta"
  #- "nvidia/llama-3.1-nemotron-ultra-253b-v1"
  #- "meta-llama/llama-4-maverick"
  #- "meta-llama/llama-4-scout"
  #- "deepseek/deepseek-r1-0528"
  #- "deepseek/deepseek-r1-0528-qwen3-8b"