# Ollama Integration Troubleshooting Guide

## Common Issues and Solutions

### 1. "Connection refused" or "Cannot connect to Ollama"

**Symptoms:**
- Error messages about connection refused
- Timeouts when trying to connect to Ollama

**Solutions:**
1. **Check if <PERSON>lla<PERSON> is running:**
   ```bash
   ollama list
   ```

2. **Start Ollama service:**
   ```bash
   ollama serve
   ```

3. **Verify <PERSON><PERSON><PERSON> is listening on the correct port:**
   ```bash
   curl http://localhost:11434/api/version
   ```

4. **Check your environment variables:**
   - Ensure `OLLAMA_HOST` or `LLM_BASE_URL` points to the correct address
   - Default should be `http://localhost:11434`

### 2. "Model not found" errors

**Symptoms:**
- Error messages about model not being available
- 404 errors when trying to use a model

**Solutions:**
1. **Pull the required models:**
   ```bash
   ollama pull hf.co/unsloth/gemma-3-1b-it-GGUF:Q4_K_M
   ollama pull hf.co/lmstudio-community/Nemotron-Research-Reasoning-Qwen-1.5B-GGUF:Q4_K_M
   ollama pull hf.co/lmstudio-community/ZR1-1.5B-GGUF:Q4_K_M
   ```

2. **List available models:**
   ```bash
   ollama list
   ```

3. **Check model names in `game_config.yaml` match exactly**

### 3. JSON parsing errors in structured responses

**Symptoms:**
- "Could not parse JSON response" warnings
- Fallback responses being used frequently

**Solutions:**
1. **This is expected behavior** - the code includes fallbacks
2. **Monitor game logs** to see if fallbacks are working correctly
3. **Consider adjusting the JSON instruction prompt** in `llm_call.py` if needed

### 4. Slow response times

**Symptoms:**
- Long delays between player actions
- Timeouts during game phases

**Solutions:**
1. **Check system resources:**
   - Ensure sufficient RAM (models require 2-4GB each)
   - Monitor CPU usage during model execution

2. **Adjust model parameters:**
   - Reduce `num_predict` in `llm_call.py` for shorter responses
   - Consider using smaller models if performance is critical

3. **Use GPU acceleration (if available):**
   - Ollama automatically uses GPU if available
   - Check with `ollama ps` during execution

### 5. Import errors

**Symptoms:**
- "ModuleNotFoundError: No module named 'ollama'"
- Import failures in Python

**Solutions:**
1. **Install the ollama package:**
   ```bash
   pip install ollama
   ```

2. **Verify installation:**
   ```bash
   python -c "import ollama; print('Success')"
   ```

3. **Check Python environment:**
   - Ensure you're using the correct virtual environment
   - Verify all requirements are installed: `pip install -r requirements.txt`

## Testing Your Setup

### Quick Test Script

Create a file `test_ollama.py`:

```python
from llm_call import chat_completion
import os

# Test basic functionality
try:
    response = chat_completion(
        chat_history=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say hello!"}
        ],
        player_name="TestPlayer",
        player_model_map={"TestPlayer": "hf.co/unsloth/gemma-3-1b-it-GGUF:Q4_K_M"},
        temperature=0.2
    )
    print("✅ Basic chat test passed!")
    print(f"Response: {response}")
except Exception as e:
    print(f"❌ Basic chat test failed: {e}")

# Test structured output
try:
    response = chat_completion(
        chat_history=[
            {"role": "system", "content": "You are playing a game."},
            {"role": "user", "content": "Choose between Alice and Bob."}
        ],
        player_name="TestPlayer",
        player_model_map={"TestPlayer": "hf.co/unsloth/gemma-3-1b-it-GGUF:Q4_K_M"},
        temperature=0.2,
        is_a_decision=True,
        choices=["Alice", "Bob"]
    )
    print("✅ Structured output test passed!")
    print(f"Response: {response}")
except Exception as e:
    print(f"❌ Structured output test failed: {e}")
```

Run with: `python test_ollama.py`

## Performance Optimization

### Memory Management
- Each model requires 2-4GB RAM
- Consider running fewer concurrent games if memory is limited
- Monitor with `ollama ps` during execution

### Response Time Tuning
- Adjust `num_predict` in `llm_call.py` (lower = faster, shorter responses)
- Use smaller models for faster responses
- Consider model-specific optimizations

### System Requirements
- **Minimum**: 8GB RAM, 4-core CPU
- **Recommended**: 16GB RAM, 8-core CPU, GPU support
- **Storage**: 10-20GB for models

## Getting Help

1. **Check Ollama documentation**: https://ollama.ai/docs
2. **Verify model availability**: https://ollama.ai/library
3. **Review game logs** in the `game_logs/` directory
4. **Enable debug output** by adding print statements to `llm_call.py`
