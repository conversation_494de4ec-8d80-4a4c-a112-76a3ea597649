#!/usr/bin/env python3
"""
Test script to verify all 8 Ollama models work correctly with the Town of Salem LLM integration.
"""

import os
import sys
from dotenv import load_dotenv
from llm_call import chat_completion

# Load environment variables
load_dotenv()

# List of all models from game_config.yaml
MODELS = [
    "hf.co/unsloth/gemma-3-1b-it-GGUF:Q4_K_M",
    "hf.co/lmstudio-community/Nemotron-Research-Reasoning-Qwen-1.5B-GGUF:Q4_K_M",
    "hf.co/lmstudio-community/ZR1-1.5B-GGUF:Q4_K_M",
    "hf.co/lmstudio-community/internlm2_5-1_8b-chat-GGUF:Q4_K_M",
    "hf.co/mradermacher/MiniCPM4-0.5B-GGUF:Q4_K_M",
    "hf.co/lmstudio-community/granite-3.1-1b-a400m-instruct-GGUF:Q4_K_M",
    "hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M",
    "hf.co/unsloth/Llama-3.2-1B-Instruct-GGUF:Q4_K_M"
]

def test_basic_chat(model_name):
    """Test basic chat functionality with a model."""
    try:
        response = chat_completion(
            chat_history=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'Hello' and nothing else."}
            ],
            player_name="TestPlayer",
            player_model_map={"TestPlayer": model_name},
            temperature=0.2
        )
        return True, response.strip()
    except Exception as e:
        return False, str(e)

def test_structured_output(model_name):
    """Test structured output (decision making) with a model."""
    try:
        response = chat_completion(
            chat_history=[
                {"role": "system", "content": "You are playing a voting game."},
                {"role": "user", "content": "Choose between Alice and Bob. Provide reasoning."}
            ],
            player_name="TestPlayer",
            player_model_map={"TestPlayer": model_name},
            temperature=0.2,
            is_a_decision=True,
            choices=["Alice", "Bob"]
        )
        # Check if response has the expected attributes
        if hasattr(response, 'reasoning') and hasattr(response, 'vote'):
            return True, f"Vote: {response.vote}, Reasoning: {response.reasoning[:50]}..."
        else:
            return False, f"Invalid response structure: {response}"
    except Exception as e:
        return False, str(e)

def main():
    """Run tests on all models."""
    print("🧪 Testing all Ollama models for Town of Salem compatibility...")
    print("=" * 80)
    
    results = []
    
    for i, model in enumerate(MODELS, 1):
        print(f"\n[{i}/8] Testing: {model}")
        print("-" * 60)
        
        # Test basic chat
        print("  📝 Basic chat test...", end=" ")
        basic_success, basic_result = test_basic_chat(model)
        if basic_success:
            print("✅ PASSED")
            print(f"     Response: {basic_result}")
        else:
            print("❌ FAILED")
            print(f"     Error: {basic_result}")
        
        # Test structured output
        print("  🎯 Structured output test...", end=" ")
        struct_success, struct_result = test_structured_output(model)
        if struct_success:
            print("✅ PASSED")
            print(f"     Response: {struct_result}")
        else:
            print("❌ FAILED")
            print(f"     Error: {struct_result}")
        
        # Record results
        results.append({
            'model': model,
            'basic_chat': basic_success,
            'structured_output': struct_success,
            'overall': basic_success and struct_success
        })
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    working_models = 0
    for result in results:
        status = "✅ WORKING" if result['overall'] else "❌ ISSUES"
        print(f"{status} - {result['model']}")
        if result['overall']:
            working_models += 1
    
    print(f"\n🎯 {working_models}/{len(MODELS)} models are fully functional")
    
    if working_models >= 8:
        print("🎉 All models are ready for Town of Salem!")
        return 0
    else:
        print("⚠️  Some models have issues. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
