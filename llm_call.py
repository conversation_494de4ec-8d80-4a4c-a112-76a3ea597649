import os
import time
from typing import List, Dict, Optional
import ollama
from pydantic import BaseModel, create_model
from typing import Literal
from dotenv import load_dotenv
import json

# Define retry constants
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 2 # Simple fixed delay

# Constrain vote to predefined options
class Vote(BaseModel):
    reasoning: str
    vote: Literal["<PERSON>", "<PERSON>", "<PERSON>", "Cem"]

def chat_completion(
    chat_history: List[Dict[str, str]],
    temperature: float = 0.2,
    player_name: str = "Player",
    player_model_map: Optional[Dict[str, str]] = None,
    is_a_decision: bool = False,
    choices: List[str] = None,
    round: Optional[int] = 1,
) -> str | Vote:

    # For Ollama, we use the host environment variable or default to localhost
    ollama_host = os.getenv("OLLAMA_HOST", "http://localhost:11434")

    # Ollama doesn't require API keys for local usage
    # But we keep the environment variable check for backward compatibility
    base_url_env = os.getenv("LLM_BASE_URL", ollama_host)

    base_url = base_url_env.rstrip('/')
    
    # Determine the model to use
    model_to_use: Optional[str] = None 
    if player_model_map and player_name in player_model_map:
        model_to_use = player_model_map[player_name]
    else:
        error_message: str
        if player_model_map is None:
            error_message = f"player_model_map is None, and no model specified for player '{player_name}'."
        elif player_name not in player_model_map: # player_model_map is not None here
             error_message = f"Player '{player_name}' not found as a key in the provided player_model_map."
        else: # Should not happen given the outer if condition, but as a safeguard
            error_message = f"Could not determine model for player '{player_name}' with the provided player_model_map."
        
        raise ValueError(error_message + " No fallback model resolution implemented for this case.")

    # Create Ollama client
    # Note: Ollama client is initialized differently than OpenAI
    # We'll use the ollama.Client() or direct API calls
    client = ollama.Client(host=base_url)

    # Remove provider-specific optimizations since we're using Ollama
    # No need for ":nitro" suffix or other provider-specific modifications
    
    # Initialize request_params for Ollama
    request_params = {
        "model": model_to_use,
        "messages": chat_history.copy(), # Use a copy
        "options": {
            "temperature": temperature,
            "num_predict": 4096 * round,  # Ollama uses num_predict instead of max_tokens
        }
    }

    # Remove Anthropic-specific features since we're using Ollama
    # Ollama doesn't support provider-specific features like thinking mode


    # Create DynamicVote class for structured responses
    DynamicVote = None
    if is_a_decision:
        if not choices:
            raise ValueError("choices must be provided when is_a_decision is True")

        # Create dynamic vote model
        DynamicVote = create_model(
            "DynamicVote",
            reasoning=(str, ...),
            vote=(Literal[tuple(choices)], ...)  # Dynamically constrain vote
        )

        # For Ollama, we need to handle structured output differently
        # We'll add a system message to request JSON format

        # Add JSON format instruction to the messages
        json_instruction = {
            "role": "system",
            "content": f"You must respond with valid JSON in this exact format: {{\"reasoning\": \"your reasoning here\", \"vote\": \"your choice\"}}. Your vote must be one of: {', '.join(choices)}. Do not include any text outside the JSON."
        }
        request_params["messages"].append(json_instruction)
        
    last_exception = None
    for attempt in range(MAX_RETRIES):
        try:
            # Use Ollama's chat method
            response = client.chat(**request_params)

            if not is_a_decision:
                # For regular chat, return the message content
                return response['message']['content']
            else:
                # For decisions, parse JSON response
                content = response['message']['content'].strip()
                try:
                    # Try to parse JSON response
                    parsed_response = json.loads(content)
                    # Create and return DynamicVote object
                    return DynamicVote(
                        reasoning=parsed_response.get('reasoning', ''),
                        vote=parsed_response.get('vote', '')
                    )
                except json.JSONDecodeError:
                    # If JSON parsing fails, try to extract vote from text
                    print(f"Warning: Could not parse JSON response: {content}")
                    # Fallback: look for any of the choices in the response
                    content_lower = content.lower()
                    for choice in choices:
                        if choice.lower() in content_lower:
                            return DynamicVote(
                                reasoning=f"Extracted from response: {content}",
                                vote=choice
                            )
                    # If no choice found, use first choice as fallback
                    return DynamicVote(
                        reasoning=f"Fallback from unparseable response: {content}",
                        vote=choices[0] if choices else "Pass"
                    )

        except Exception as e:
            last_exception = e
            print(f"Error during chat completion on attempt {attempt + 1}/{MAX_RETRIES}: {type(e).__name__}: {str(e)}")

            if attempt < MAX_RETRIES - 1:
                print(f"Waiting {RETRY_DELAY_SECONDS} seconds before next retry...")
                time.sleep(RETRY_DELAY_SECONDS)
            else:
                # This is the last attempt
                print(f"\n\nAll {MAX_RETRIES} retry attempts failed for model {model_to_use}.")
                print(f"Final error details:")
                print(f"  Ollama host: {base_url}")
                print(f"  Model used: {model_to_use}")
                print(f"  Request params (model and message count): model={request_params.get('model')}, num_messages={len(request_params.get('messages', []))}")
                print(f"  Last exception: {type(last_exception).__name__}: {str(last_exception)}\n\n")

                raise Exception
                    

    # This part of the code should not be reached if MAX_RETRIES > 0,
    # as the loop will either return on success or raise on the final failed attempt.
    if last_exception:
        raise last_exception # Should be already raised from the loop
    else:
        # This case is logically very unlikely if MAX_RETRIES > 0.
        raise RuntimeError("Chat completion failed unexpectedly without a caught exception after retry loop.")


if __name__ == "__main__":
    load_dotenv()
    # 1. Define the player name
    player_to_use = "Max"

    # 2. Define the player_model_map with Ollama model
    model_map = {
        "Max": "hf.co/unsloth/gemma-3-1b-it-GGUF:Q4_K_M",
    }

    # 3. Create a sample chat history
    sample_history = [
        {"role": "system", "content": "You are an assistant."},
        {"role": "user", "content": "Alice is a liar. Bob is a pervert. Charlie is foul mouthed. Cem is an alcoholic. Who are you choosing to vote out and why?"},
    ]

    # 4. Call the function
    print(f"Attempting chat completion for player: {player_to_use}")
    response_content = chat_completion(
        chat_history=sample_history,
        player_name=player_to_use,
        player_model_map=model_map,
        temperature=0.2,
        is_a_decision=True,
        choices=["Alice","Bob", "Charlie", "Cem"]
    )
    print(f"\nModel ({model_map[player_to_use]}) response for {player_to_use}:")
    print(response_content)